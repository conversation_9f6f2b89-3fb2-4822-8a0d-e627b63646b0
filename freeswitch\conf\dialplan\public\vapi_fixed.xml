<?xml version="1.0" encoding="utf-8"?>
<include>
  <!-- VAPI Integration - Fixed Configuration for "phone number not found" issue -->
  
  <extension name="vapi_number_fix">
    <condition field="destination_number" expression="^(919228030045|9228030045|\+919228030045)$">
      
      <!-- Answer immediately to establish the call -->
      <action application="answer"/>
      
      <!-- Critical fix: Set the exact number format that VAPI expects -->
      <action application="set" data="vapi_registered_number=919228030045"/>
      <action application="set" data="vapi_domain=a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai"/>
      
      <!-- VAPI Authentication -->
      <action application="set" data="vapi_api_key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
      <action application="set" data="vapi_version=2023-11-01"/>
      
      <!-- Fix 1: Set caller ID to match VAPI registered number exactly -->
      <action application="set" data="effective_caller_id_number=${vapi_registered_number}"/>
      <action application="set" data="effective_caller_id_name=VAPI-Call"/>
      
      <!-- Fix 2: Export authentication headers with nolocal to ensure they reach VAPI -->
      <action application="export" data="nolocal:sip_h_X-VAPI-Key=${vapi_api_key}"/>
      <action application="export" data="nolocal:sip_h_X-Version=${vapi_version}"/>
      
      <!-- Fix 3: Critical - Set From URI to use the registered number -->
      <!-- This is often the root cause of "phone number not found" -->
      <action application="export" data="nolocal:sip_from_uri=sip:${vapi_registered_number}@*************"/>
      
      <!-- Fix 4: Set To URI to use the registered number at VAPI domain -->
      <action application="export" data="nolocal:sip_to_uri=sip:${vapi_registered_number}@${vapi_domain}"/>
      
      <!-- Fix 5: Set Contact header properly -->
      <action application="export" data="nolocal:sip_contact_uri=sip:${vapi_registered_number}@*************:5080"/>
      
      <!-- Fix 6: Add P-Asserted-Identity header (some SIP services require this) -->
      <action application="export" data="nolocal:sip_h_P-Asserted-Identity=sip:${vapi_registered_number}@*************"/>
      
      <!-- Fix 7: Set Remote-Party-ID header -->
      <action application="export" data="nolocal:sip_h_Remote-Party-ID=${vapi_registered_number}"/>
      
      <!-- Fix 8: Ensure the Request-URI uses the correct format -->
      <action application="export" data="nolocal:sip_req_uri=sip:${vapi_registered_number}@${vapi_domain}"/>
      
      <!-- Fix 9: Set User-Agent to identify FreeSWITCH -->
      <action application="export" data="nolocal:sip_h_User-Agent=FreeSWITCH-VAPI-Gateway"/>
      
      <!-- Fix 10: Add custom headers that VAPI might expect -->
      <action application="export" data="nolocal:sip_h_X-Forwarded-For=*************"/>
      <action application="export" data="nolocal:sip_h_X-Original-To=${vapi_registered_number}"/>
      
      <!-- Enable SIP tracing for debugging -->
      <action application="set" data="sip_trace=yes"/>
      
      <!-- Log the exact configuration being used -->
      <action application="log" data="INFO === VAPI CALL CONFIGURATION ==="/>
      <action application="log" data="INFO Registered Number: ${vapi_registered_number}"/>
      <action application="log" data="INFO VAPI Domain: ${vapi_domain}"/>
      <action application="log" data="INFO From URI: sip:${vapi_registered_number}@*************"/>
      <action application="log" data="INFO To URI: sip:${vapi_registered_number}@${vapi_domain}"/>
      <action application="log" data="INFO API Key: ${vapi_api_key}"/>
      
      <!-- Bridge to VAPI with all the fixes applied -->
      <action application="bridge" data="sofia/external/${vapi_registered_number}@${vapi_domain}"/>
      
      <!-- Error handling if bridge fails -->
      <action application="log" data="ERROR VAPI bridge failed - Cause: ${hangup_cause}"/>
      <action application="log" data="ERROR Bridge hangup cause: ${bridge_hangup_cause}"/>
      
      <!-- Play busy tone and hangup -->
      <action application="playback" data="tone_stream://%(500,0,480,620);loops=3"/>
      <action application="hangup" data="CALL_REJECTED"/>
    </condition>
  </extension>
  
  <!-- Alternative extension with different number format attempts -->
  <extension name="vapi_number_variants">
    <condition field="destination_number" expression="^(\+91|91|0091)?9228030045$">
      
      <action application="log" data="INFO Trying alternative number format for: ${destination_number}"/>
      <action application="transfer" data="919228030045 XML public"/>
    </condition>
  </extension>
</include>
