#!/usr/bin/env python3
"""
VAPI Connection Test Script
Tests the VAPI SIP connection and validates configuration
"""

import socket
import sys
import time
import subprocess
import json
from datetime import datetime

# VAPI Configuration
VAPI_CONFIG = {
    'domain': 'a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai',
    'number': '919228030045',
    'api_key': 'ee2abd78-2c93-4fba-bb2e-1dba77944e40',
    'version': '2023-11-01'
}

FREESWITCH_CONFIG = {
    'ip': '*************',
    'sip_port': 5080,
    'cli_port': 8021
}

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def test_dns_resolution():
    """Test if VAPI domain resolves"""
    print_header("DNS Resolution Test")
    
    try:
        import socket
        result = socket.gethostbyname(VAPI_CONFIG['domain'])
        print(f"✓ VAPI domain resolves to: {result}")
        return True
    except socket.gaierror as e:
        print(f"✗ DNS resolution failed: {e}")
        return False

def test_sip_connectivity():
    """Test SIP connectivity to VAPI"""
    print_header("SIP Connectivity Test")
    
    try:
        # Try to resolve VAPI domain and connect
        vapi_ip = socket.gethostbyname(VAPI_CONFIG['domain'])
        
        # Test SIP port (usually 5060)
        for port in [5060, 5061]:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)
            try:
                # Send a basic SIP OPTIONS request
                sip_options = f"""OPTIONS sip:{VAPI_CONFIG['domain']} SIP/2.0
Via: SIP/2.0/UDP {FREESWITCH_CONFIG['ip']}:5080;branch=z9hG4bK-test
From: <sip:test@{FREESWITCH_CONFIG['ip']}>;tag=test
To: <sip:{VAPI_CONFIG['domain']}>
Call-ID: test-{int(time.time())}@{FREESWITCH_CONFIG['ip']}
CSeq: 1 OPTIONS
Max-Forwards: 70
Content-Length: 0

"""
                sock.sendto(sip_options.encode(), (vapi_ip, port))
                response = sock.recv(1024)
                print(f"✓ SIP response received on port {port}")
                print(f"  Response: {response.decode()[:100]}...")
                sock.close()
                return True
            except socket.timeout:
                print(f"✗ No SIP response on port {port}")
            except Exception as e:
                print(f"✗ SIP test failed on port {port}: {e}")
            finally:
                sock.close()
        
        return False
    except Exception as e:
        print(f"✗ SIP connectivity test failed: {e}")
        return False

def validate_configuration():
    """Validate the VAPI configuration"""
    print_header("Configuration Validation")
    
    issues = []
    
    # Check number format
    number = VAPI_CONFIG['number']
    if not number.isdigit():
        issues.append("Number contains non-digit characters")
    if len(number) != 12:
        issues.append(f"Number length is {len(number)}, expected 12 digits")
    if not number.startswith('91'):
        issues.append("Number should start with country code 91")
    
    # Check API key format
    api_key = VAPI_CONFIG['api_key']
    if len(api_key) != 36:
        issues.append(f"API key length is {len(api_key)}, expected 36 characters")
    if api_key.count('-') != 4:
        issues.append("API key should have 4 hyphens (UUID format)")
    
    # Check domain format
    domain = VAPI_CONFIG['domain']
    if not domain.endswith('.sip.vapi.ai'):
        issues.append("Domain should end with .sip.vapi.ai")
    
    if issues:
        print("✗ Configuration issues found:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✓ Configuration appears valid")
        return True

def generate_test_sip_invite():
    """Generate a test SIP INVITE message"""
    print_header("Test SIP INVITE Message")
    
    call_id = f"test-{int(time.time())}@{FREESWITCH_CONFIG['ip']}"
    
    sip_invite = f"""INVITE sip:{VAPI_CONFIG['number']}@{VAPI_CONFIG['domain']} SIP/2.0
Via: SIP/2.0/UDP {FREESWITCH_CONFIG['ip']}:{FREESWITCH_CONFIG['sip_port']};branch=z9hG4bK-test123
Max-Forwards: 70
From: <sip:{VAPI_CONFIG['number']}@{FREESWITCH_CONFIG['ip']}>;tag=test123
To: <sip:{VAPI_CONFIG['number']}@{VAPI_CONFIG['domain']}>
Call-ID: {call_id}
CSeq: 1 INVITE
Contact: <sip:{VAPI_CONFIG['number']}@{FREESWITCH_CONFIG['ip']}:{FREESWITCH_CONFIG['sip_port']}>
Content-Type: application/sdp
Content-Length: 0
User-Agent: FreeSWITCH-VAPI-Test
X-VAPI-Key: {VAPI_CONFIG['api_key']}
X-Version: {VAPI_CONFIG['version']}
X-Original-Called-Number: {VAPI_CONFIG['number']}

"""
    
    print("This is the SIP INVITE that should be sent to VAPI:")
    print("-" * 50)
    print(sip_invite)
    print("-" * 50)
    
    # Validate critical headers
    print("\nCritical header validation:")
    headers_to_check = [
        ('From', f'sip:{VAPI_CONFIG["number"]}@{FREESWITCH_CONFIG["ip"]}'),
        ('To', f'sip:{VAPI_CONFIG["number"]}@{VAPI_CONFIG["domain"]}'),
        ('X-VAPI-Key', VAPI_CONFIG['api_key']),
        ('X-Version', VAPI_CONFIG['version'])
    ]
    
    for header_name, expected_value in headers_to_check:
        if expected_value in sip_invite:
            print(f"✓ {header_name} header is correct")
        else:
            print(f"✗ {header_name} header may be incorrect")

def test_freeswitch_status():
    """Test FreeSWITCH status"""
    print_header("FreeSWITCH Status Test")
    
    try:
        # Try to connect to FreeSWITCH CLI
        result = subprocess.run([
            'fs_cli', '-H', FREESWITCH_CONFIG['ip'], '-P', str(FREESWITCH_CONFIG['cli_port']),
            '-x', 'status'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ FreeSWITCH is running and accessible")
            print(f"Status output: {result.stdout[:200]}...")
            return True
        else:
            print(f"✗ FreeSWITCH CLI failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("✗ FreeSWITCH CLI connection timed out")
        return False
    except FileNotFoundError:
        print("✗ fs_cli command not found")
        return False
    except Exception as e:
        print(f"✗ FreeSWITCH test failed: {e}")
        return False

def main():
    """Main test function"""
    print(f"VAPI Integration Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Configuration Validation", validate_configuration),
        ("DNS Resolution", test_dns_resolution),
        ("SIP Connectivity", test_sip_connectivity),
        ("FreeSWITCH Status", test_freeswitch_status)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Generate test SIP message
    generate_test_sip_invite()
    
    # Summary
    print_header("Test Summary")
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The configuration looks good.")
        print("If you're still getting 'phone number not found', check:")
        print("1. VAPI dashboard for exact number registration format")
        print("2. SIP traces for actual headers being sent")
        print("3. VAPI account status and API key validity")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
