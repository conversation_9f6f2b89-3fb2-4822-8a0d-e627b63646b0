#!/bin/bash

# FreeSWITCH VAPI Integration Debug Script
# This script helps debug the VAPI integration issues

echo "=== FreeSWITCH VAPI Debug Tool ==="
echo "Date: $(date)"
echo ""

# Configuration
FREESWITCH_IP="*************"
FREESWITCH_PORT="5080"
VAPI_DOMAIN="a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai"
VAPI_NUMBER="919228030045"
VAPI_API_KEY="ee2abd78-2c93-4fba-bb2e-1dba77944e40"

echo "Configuration:"
echo "  FreeSWITCH: ${FREESWITCH_IP}:${FREESWITCH_PORT}"
echo "  VAPI Domain: ${VAPI_DOMAIN}"
echo "  VAPI Number: ${VAPI_NUMBER}"
echo "  API Key: ${VAPI_API_KEY}"
echo ""

# Function to test SIP connectivity
test_sip_connectivity() {
    echo "=== Testing SIP Connectivity ==="
    
    # Test if FreeSWITCH is listening
    echo "1. Testing FreeSWITCH SIP port..."
    if command -v nc >/dev/null 2>&1; then
        if nc -z -v -w5 ${FREESWITCH_IP} ${FREESWITCH_PORT} 2>&1; then
            echo "   ✓ FreeSWITCH SIP port is accessible"
        else
            echo "   ✗ FreeSWITCH SIP port is not accessible"
        fi
    else
        echo "   ! netcat not available, skipping port test"
    fi
    
    # Test VAPI domain resolution
    echo "2. Testing VAPI domain resolution..."
    if command -v nslookup >/dev/null 2>&1; then
        if nslookup ${VAPI_DOMAIN} >/dev/null 2>&1; then
            echo "   ✓ VAPI domain resolves"
            nslookup ${VAPI_DOMAIN} | grep "Address:" | head -1
        else
            echo "   ✗ VAPI domain does not resolve"
        fi
    else
        echo "   ! nslookup not available, skipping DNS test"
    fi
    echo ""
}

# Function to generate test SIP message
generate_test_sip() {
    echo "=== Sample SIP INVITE for VAPI ==="
    cat << EOF
INVITE sip:${VAPI_NUMBER}@${VAPI_DOMAIN} SIP/2.0
Via: SIP/2.0/UDP ${FREESWITCH_IP}:${FREESWITCH_PORT};branch=z9hG4bK-test123
Max-Forwards: 70
From: <sip:${VAPI_NUMBER}@${FREESWITCH_IP}>;tag=test123
To: <sip:${VAPI_NUMBER}@${VAPI_DOMAIN}>
Call-ID: test-call-$(date +%s)@${FREESWITCH_IP}
CSeq: 1 INVITE
Contact: <sip:${VAPI_NUMBER}@${FREESWITCH_IP}:${FREESWITCH_PORT}>
Content-Type: application/sdp
Content-Length: 0
User-Agent: FreeSWITCH-VAPI-Test
X-VAPI-Key: ${VAPI_API_KEY}
X-Version: 2023-11-01
X-Original-Called-Number: ${VAPI_NUMBER}
X-Called-Number: ${VAPI_NUMBER}

EOF
    echo ""
}

# Function to check FreeSWITCH logs
check_freeswitch_logs() {
    echo "=== Checking FreeSWITCH Logs ==="
    
    # Common log locations
    LOG_LOCATIONS=(
        "/var/log/freeswitch/freeswitch.log"
        "/usr/local/freeswitch/log/freeswitch.log"
        "/opt/freeswitch/log/freeswitch.log"
        "./freeswitch.log"
    )
    
    for log_path in "${LOG_LOCATIONS[@]}"; do
        if [ -f "$log_path" ]; then
            echo "Found log file: $log_path"
            echo "Recent VAPI-related entries:"
            tail -50 "$log_path" | grep -i "vapi\|919228030045\|a2a78994" || echo "No VAPI entries found"
            echo ""
            break
        fi
    done
    
    echo "If no logs found, check your FreeSWITCH installation and log configuration."
    echo ""
}

# Function to validate VAPI configuration
validate_vapi_config() {
    echo "=== VAPI Configuration Validation ==="
    
    echo "Checking number format consistency:"
    echo "  Cloud Connect Number: +919228030045"
    echo "  VAPI Registered Number: ${VAPI_NUMBER}"
    echo "  FreeSWITCH Destination: ${VAPI_NUMBER}"
    
    if [ "${VAPI_NUMBER}" = "919228030045" ]; then
        echo "  ✓ Number formats are consistent"
    else
        echo "  ✗ Number format mismatch detected!"
    fi
    echo ""
    
    echo "Key troubleshooting points:"
    echo "1. Ensure VAPI number is registered exactly as: ${VAPI_NUMBER}"
    echo "2. Check that API key is valid and active"
    echo "3. Verify SIP headers match VAPI's expected format"
    echo "4. Confirm To/From URIs use the exact registered number"
    echo ""
}

# Function to show recommended FreeSWITCH commands
show_freeswitch_commands() {
    echo "=== Useful FreeSWITCH CLI Commands ==="
    echo ""
    echo "Connect to FreeSWITCH CLI:"
    echo "  fs_cli -H ${FREESWITCH_IP} -P 8021"
    echo ""
    echo "Debug commands to run in fs_cli:"
    echo "  sofia status"
    echo "  sofia status profile external"
    echo "  sofia loglevel all 9"
    echo "  console loglevel debug"
    echo "  show calls"
    echo "  show channels"
    echo ""
    echo "To trace SIP messages:"
    echo "  sofia profile external siptrace on"
    echo "  sofia global siptrace on"
    echo ""
    echo "To test the dialplan:"
    echo "  originate sofia/external/919228030045@${VAPI_DOMAIN} &echo"
    echo ""
}

# Main execution
main() {
    test_sip_connectivity
    generate_test_sip
    validate_vapi_config
    check_freeswitch_logs
    show_freeswitch_commands
    
    echo "=== Next Steps ==="
    echo "1. Review the generated SIP message format above"
    echo "2. Enable SIP tracing in FreeSWITCH"
    echo "3. Make a test call and capture the exact SIP messages"
    echo "4. Compare with VAPI's expected format"
    echo "5. Adjust dialplan headers as needed"
    echo ""
    echo "Common fixes for 'phone number not found':"
    echo "- Ensure To header uses exact registered number format"
    echo "- Verify From header contains the registered number"
    echo "- Check that X-VAPI-Key header is present and correct"
    echo "- Confirm the SIP domain matches VAPI's expected domain"
}

# Run the main function
main
