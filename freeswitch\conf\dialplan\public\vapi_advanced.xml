<?xml version="1.0" encoding="utf-8"?>
<include>
  <!-- Advanced VAPI Integration with Multiple Number Format Handling -->
  
  <extension name="vapi_debug_headers">
    <condition field="destination_number" expression="^(************|9228030045|\+************)$">
      
      <!-- Comprehensive logging for debugging -->
      <action application="log" data="INFO ========== VAPI CALL DEBUG =========="/>
      <action application="log" data="INFO Call UUID: ${uuid}"/>
      <action application="log" data="INFO Timestamp: ${strftime(%Y-%m-%d %H:%M:%S)}"/>
      <action application="log" data="INFO Source IP: ${network_addr}"/>
      <action application="log" data="INFO Caller Number: ${caller_id_number}"/>
      <action application="log" data="INFO Caller Name: ${caller_id_name}"/>
      <action application="log" data="INFO Destination: ${destination_number}"/>
      <action application="log" data="INFO Original Destination: ${sip_req_uri}"/>
      <action application="log" data="INFO SIP From: ${sip_from_uri}"/>
      <action application="log" data="INFO SIP To: ${sip_to_uri}"/>
      <action application="log" data="INFO SIP Contact: ${sip_contact_uri}"/>
      <action application="log" data="INFO User Agent: ${sip_user_agent}"/>
      <action application="log" data="INFO Call-ID: ${sip_call_id}"/>
      
      <!-- Answer the call -->
      <action application="answer"/>
      <action application="sleep" data="1000"/>
      
      <!-- Normalize the number format -->
      <action application="set" data="normalized_number=************"/>
      
      <!-- Set VAPI configuration -->
      <action application="set" data="vapi_sip_domain=a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai"/>
      <action application="set" data="vapi_api_key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
      <action application="set" data="vapi_version=2023-11-01"/>
      
      <!-- Set caller ID to match VAPI registered number -->
      <action application="set" data="effective_caller_id_number=${normalized_number}"/>
      <action application="set" data="effective_caller_id_name=FreeSWITCH-VAPI"/>
      
      <!-- Critical: Set the proper headers for VAPI -->
      <action application="export" data="nolocal:sip_h_X-VAPI-Key=${vapi_api_key}"/>
      <action application="export" data="nolocal:sip_h_X-Version=${vapi_version}"/>
      
      <!-- Set proper SIP URIs - this is crucial for VAPI recognition -->
      <action application="export" data="nolocal:sip_from_uri=sip:${normalized_number}@*************"/>
      <action application="export" data="nolocal:sip_to_uri=sip:${normalized_number}@${vapi_sip_domain}"/>
      <action application="export" data="nolocal:sip_contact_uri=sip:${normalized_number}@*************:5080"/>
      
      <!-- Additional headers that might help VAPI identify the call -->
      <action application="export" data="nolocal:sip_h_X-Original-Called-Number=${normalized_number}"/>
      <action application="export" data="nolocal:sip_h_X-Called-Number=${normalized_number}"/>
      
      <!-- Set codec preferences for better compatibility -->
      <action application="export" data="absolute_codec_string=PCMU,PCMA"/>
      
      <!-- Enable detailed SIP logging for this call -->
      <action application="set" data="sip_trace=yes"/>
      <action application="set" data="sofia_profile_debug=9"/>
      
      <!-- Log what we're about to send to VAPI -->
      <action application="log" data="INFO ========== SENDING TO VAPI =========="/>
      <action application="log" data="INFO Target URI: sip:${normalized_number}@${vapi_sip_domain}"/>
      <action application="log" data="INFO From: sip:${normalized_number}@*************"/>
      <action application="log" data="INFO To: sip:${normalized_number}@${vapi_sip_domain}"/>
      <action application="log" data="INFO X-VAPI-Key: ${vapi_api_key}"/>
      <action application="log" data="INFO X-Version: ${vapi_version}"/>
      
      <!-- Attempt bridge to VAPI -->
      <action application="bridge" data="{sip_invite_domain=${vapi_sip_domain}}sofia/external/${normalized_number}@${vapi_sip_domain}"/>
      
      <!-- If we get here, the bridge failed -->
      <action application="log" data="ERROR ========== VAPI BRIDGE FAILED =========="/>
      <action application="log" data="ERROR Hangup Cause: ${hangup_cause}"/>
      <action application="log" data="ERROR Bridge Hangup Cause: ${bridge_hangup_cause}"/>
      <action application="log" data="ERROR Last Bridge Hangup Cause: ${last_bridge_hangup_cause}"/>
      <action application="log" data="ERROR SIP Hangup Disposition: ${sip_hangup_disposition}"/>
      
      <!-- Play error tone and hangup -->
      <action application="playback" data="tone_stream://%(500,0,480,620);loops=2"/>
      <action application="hangup" data="CALL_REJECTED"/>
    </condition>
  </extension>
</include>
