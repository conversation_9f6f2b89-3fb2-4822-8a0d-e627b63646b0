# FreeSWITCH-VAPI Integration Fix

This repository contains the configuration and debugging tools to fix the "phone number not found" error in your FreeSWITCH-VAPI integration.

## Problem Summary

Your setup has calls successfully reaching FreeSWITCH from Cloud Connect, but VAPI is responding with "phone number not found" when FreeSWITCH tries to bridge the call.

## Solution Overview

The issue is typically caused by incorrect SIP header formatting. VAPI needs to receive the exact registered phone number in the correct SIP headers with proper authentication.

## Files Included

### Configuration Files
- `freeswitch/conf/sip_profiles/external.xml` - SIP profile configuration
- `freeswitch/conf/dialplan/public/vapi_routing.xml` - Basic dialplan
- `freeswitch/conf/dialplan/public/vapi_advanced.xml` - Advanced dialplan with debugging
- `freeswitch/conf/dialplan/public/vapi_fixed.xml` - **Recommended** - Fixed dialplan addressing the issue

### Debug Tools
- `debug_vapi.sh` - Comprehensive debugging script
- `test_vapi_connection.py` - Python script to test VAPI connectivity
- `VAPI_TROUBLESHOOTING.md` - Detailed troubleshooting guide

## Quick Fix

1. **Use the fixed dialplan**:
   ```bash
   # Copy the fixed dialplan to your FreeSWITCH configuration
   cp freeswitch/conf/dialplan/public/vapi_fixed.xml /path/to/freeswitch/conf/dialplan/public/
   ```

2. **Reload FreeSWITCH dialplan**:
   ```bash
   fs_cli -x "reloadxml"
   ```

3. **Test the connection**:
   ```bash
   python3 test_vapi_connection.py
   ```

## Key Fixes Applied

### 1. Correct From Header
```xml
<action application="export" data="nolocal:sip_from_uri=sip:************@*************"/>
```

### 2. Proper To Header
```xml
<action application="export" data="nolocal:sip_to_uri=sip:<EMAIL>"/>
```

### 3. Authentication Headers
```xml
<action application="export" data="nolocal:sip_h_X-VAPI-Key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
<action application="export" data="nolocal:sip_h_X-Version=2023-11-01"/>
```

### 4. Consistent Number Format
All headers use the exact format: `************` (no + prefix, no variations)

## Testing Steps

1. **Run the debug script**:
   ```bash
   ./debug_vapi.sh
   ```

2. **Test VAPI connectivity**:
   ```bash
   python3 test_vapi_connection.py
   ```

3. **Enable SIP tracing**:
   ```bash
   fs_cli -x "sofia profile external siptrace on"
   fs_cli -x "console loglevel debug"
   ```

4. **Make a test call** and check logs for the exact SIP messages

## Expected Call Flow

1. **Cloud Connect** receives call to +************
2. **Cloud Connect** forwards to FreeSWITCH (*************:5080)
3. **FreeSWITCH** matches dialplan and sets proper headers
4. **FreeSWITCH** bridges to VAPI with authentication
5. **VAPI** recognizes the number and connects to AI assistant

## Troubleshooting

If still getting "phone number not found":

1. Check VAPI dashboard for exact number registration format
2. Verify API key is active and correct
3. Capture SIP traces and compare with expected format
4. Ensure number format matches exactly: `************`

See `VAPI_TROUBLESHOOTING.md` for detailed troubleshooting steps.

## Configuration Details

- **FreeSWITCH IP**: *************:5080
- **VAPI Domain**: a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai
- **Registered Number**: ************
- **API Key**: ee2abd78-2c93-4fba-bb2e-1dba77944e40

## Success Indicators

When working correctly:
- No "phone number not found" error in logs
- VAPI responds with 200 OK
- Call connects to AI assistant
- Audio flows both directions

## Support

If you continue to experience issues:
1. Run the debug tools and capture output
2. Enable SIP tracing and capture the exact SIP messages
3. Check VAPI dashboard for any error messages
4. Verify account status and API key permissions

The most common fix is ensuring the From header contains the exact registered number format that VAPI expects.
