<include>
  <context name="public">
    <!-- Keep existing extensions -->
    <extension name="unloop">
      <condition field="${unroll_loops}" expression="^true$"/>
      <condition field="${sip_looped_call}" expression="^true$">
        <action application="deflect" data="${destination_number}"/>
      </condition>
    </extension>

    <extension name="outside_call" continue="true">
      <condition>
        <action application="set" data="outside_call=true"/>
        <action application="export" data="RFC2822_DATE=${strftime(%a, %d %b %Y %T %z)}"/>
      </condition>
    </extension>

    <extension name="call_debug" continue="true">
      <condition field="${call_debug}" expression="^true$" break="never">
        <action application="info"/>
      </condition>
    </extension>

    <!-- FIXED VAPI Inbound Call Routing -->
    <extension name="vapi_inbound_fixed">
      <condition field="destination_number" expression="^(\+?************|**********)$">
        
        <!-- Answer the call immediately -->
        <action application="answer"/>
        
        <!-- Set VAPI configuration variables -->
        <action application="set" data="vapi_number=************"/>
        <action application="set" data="vapi_domain=a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai"/>
        <action application="set" data="vapi_api_key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
        <action application="set" data="vapi_version=2023-11-01"/>
        
        <!-- CRITICAL FIX 1: Set caller ID to the VAPI registered number -->
        <action application="set" data="effective_caller_id_number=${vapi_number}"/>
        <action application="set" data="effective_caller_id_name=VAPI-Call"/>
        
        <!-- CRITICAL FIX 2: Use 'export' with 'nolocal:' to ensure headers reach VAPI -->
        <action application="export" data="nolocal:sip_h_X-VAPI-Key=${vapi_api_key}"/>
        <action application="export" data="nolocal:sip_h_X-Version=${vapi_version}"/>
        <action application="export" data="nolocal:sip_h_User-Agent=FreeSWITCH-VAPI-Gateway"/>
        
        <!-- CRITICAL FIX 3: Set From header to use VAPI registered number -->
        <!-- This is the most common cause of "phone number not found" -->
        <action application="export" data="nolocal:sip_from_uri=sip:${vapi_number}@*************"/>
        
        <!-- CRITICAL FIX 4: Set To header to use VAPI registered number at VAPI domain -->
        <action application="export" data="nolocal:sip_to_uri=sip:${vapi_number}@${vapi_domain}"/>
        
        <!-- CRITICAL FIX 5: Set Contact header properly -->
        <action application="export" data="nolocal:sip_contact_uri=sip:${vapi_number}@*************:5080"/>
        
        <!-- Additional headers that help VAPI identify the call -->
        <action application="export" data="nolocal:sip_h_P-Asserted-Identity=sip:${vapi_number}@*************"/>
        <action application="export" data="nolocal:sip_h_Remote-Party-ID=${vapi_number}"/>
        <action application="export" data="nolocal:sip_h_X-Original-Called-Number=${vapi_number}"/>
        <action application="export" data="nolocal:sip_h_X-Called-Number=${vapi_number}"/>
        
        <!-- Enable SIP tracing for debugging -->
        <action application="set" data="sip_trace=yes"/>
        
        <!-- Log the call details for debugging -->
        <action application="log" data="INFO ===== VAPI CALL START ====="/>
        <action application="log" data="INFO Original Caller: ${caller_id_number}"/>
        <action application="log" data="INFO Destination: ${destination_number}"/>
        <action application="log" data="INFO VAPI Number: ${vapi_number}"/>
        <action application="log" data="INFO VAPI Domain: ${vapi_domain}"/>
        <action application="log" data="INFO From URI: sip:${vapi_number}@*************"/>
        <action application="log" data="INFO To URI: sip:${vapi_number}@${vapi_domain}"/>
        <action application="log" data="INFO API Key: ${vapi_api_key}"/>
        
        <!-- CRITICAL FIX 6: Bridge using the correct format -->
        <action application="bridge" data="sofia/external/${vapi_number}@${vapi_domain}"/>
        
        <!-- Error handling if bridge fails -->
        <action application="log" data="ERROR VAPI bridge failed - Hangup Cause: ${hangup_cause}"/>
        <action application="log" data="ERROR Bridge Hangup Cause: ${bridge_hangup_cause}"/>
        <action application="playback" data="tone_stream://%(500,0,480,620);loops=3"/>
        <action application="hangup" data="CALL_REJECTED"/>
      </condition>
    </extension>

    <!-- Keep existing public extensions -->
    <extension name="public_extensions">
      <condition field="destination_number" expression="^(10[01][0-9])$">
        <action application="transfer" data="$1 XML default"/>
      </condition>
    </extension>

    <extension name="public_conference_extensions">
      <condition field="destination_number" expression="^(3[5-8][01][0-9])$">
        <action application="transfer" data="$1 XML default"/>
      </condition>
    </extension>

    <!-- Include any additional public configurations -->
    <X-PRE-PROCESS cmd="include" data="public/*.xml"/>
  </context>
</include>
