<?xml version="1.0" encoding="utf-8"?>
<profile name="external">
  <gateways>
  </gateways>
  
  <domains>
    <domain name="all" alias="false" parse="true"/>
  </domains>
  
  <settings>
    <!-- Basic SIP settings -->
    <param name="debug" value="0"/>
    <param name="sip-trace" value="yes"/>
    <param name="sip-capture" value="yes"/>
    <param name="watchdog-enabled" value="no"/>
    <param name="watchdog-step-timeout" value="30000"/>
    <param name="watchdog-event-timeout" value="30000"/>
    <param name="log-auth-failures" value="true"/>
    <param name="forward-unsolicited-mwi-notify" value="false"/>
    
    <!-- Network settings -->
    <param name="sip-ip" value="*************"/>
    <param name="sip-port" value="5080"/>
    <param name="ext-rtp-ip" value="*************"/>
    <param name="ext-sip-ip" value="*************"/>
    <param name="rtp-ip" value="*************"/>
    
    <!-- RTP settings -->
    <param name="rtp-timer-name" value="soft"/>
    <param name="rtp-timeout-sec" value="300"/>
    <param name="rtp-hold-timeout-sec" value="1800"/>
    
    <!-- Codec settings -->
    <param name="inbound-codec-prefs" value="PCMU,PCMA,G729"/>
    <param name="outbound-codec-prefs" value="PCMU,PCMA,G729"/>
    <param name="inbound-codec-negotiation" value="generous"/>
    
    <!-- Authentication and security -->
    <param name="auth-calls" value="false"/>
    <param name="accept-blind-auth" value="true"/>
    <param name="accept-blind-reg" value="false"/>
    
    <!-- DTMF settings -->
    <param name="dtmf-duration" value="2000"/>
    <param name="dtmf-type" value="rfc2833"/>
    
    <!-- Session settings -->
    <param name="session-timeout" value="1800"/>
    <param name="multiple-registrations" value="contact"/>
    
    <!-- Context for incoming calls -->
    <param name="context" value="public"/>
    
    <!-- Caller ID settings -->
    <param name="caller-id-in-from" value="false"/>
    
    <!-- SIP message handling -->
    <param name="aggressive-nat-detection" value="true"/>
    <param name="inbound-use-callid-as-uuid" value="false"/>
    <param name="outbound-use-uuid-as-callid" value="false"/>
    
    <!-- Enable proper header handling for VAPI -->
    <param name="pass-callee-id" value="false"/>
    <param name="enable-100rel" value="false"/>
    <param name="disable-transfer" value="false"/>
    <param name="manual-redirect" value="false"/>
    
    <!-- NAT handling -->
    <param name="apply-nat-acl" value="nat.auto"/>
    <param name="local-network-acl" value="localnet.auto"/>
    
    <!-- Challenge handling -->
    <param name="challenge-realm" value="auto_from"/>
  </settings>
</profile>
