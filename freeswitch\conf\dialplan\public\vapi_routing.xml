<?xml version="1.0" encoding="utf-8"?>
<include>
  <!-- VAPI Integration Dialplan -->
  <!-- This handles calls coming from Cloud Connect for number 919228030045 -->
  
  <extension name="vapi_inbound">
    <condition field="destination_number" expression="^(919228030045|9228030045|\+919228030045)$">
      
      <!-- Log the incoming call for debugging -->
      <action application="log" data="INFO === VAPI CALL START ==="/>
      <action application="log" data="INFO Caller ID: ${caller_id_number}"/>
      <action application="log" data="INFO Destination: ${destination_number}"/>
      <action application="log" data="INFO From: ${sip_from_uri}"/>
      <action application="log" data="INFO To: ${sip_to_uri}"/>
      <action application="log" data="INFO Contact: ${sip_contact_uri}"/>
      
      <!-- Answer the call immediately -->
      <action application="answer"/>
      
      <!-- Set variables for VAPI integration -->
      <action application="set" data="vapi_number=919228030045"/>
      <action application="set" data="vapi_domain=a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai"/>
      <action application="set" data="vapi_api_key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
      <action application="set" data="vapi_version=2023-11-01"/>
      
      <!-- Set proper caller ID for VAPI -->
      <action application="set" data="effective_caller_id_number=919228030045"/>
      <action application="set" data="effective_caller_id_name=CloudConnect"/>
      
      <!-- Set SIP headers for VAPI authentication -->
      <action application="export" data="sip_h_X-VAPI-Key=${vapi_api_key}"/>
      <action application="export" data="sip_h_X-Version=${vapi_version}"/>
      
      <!-- Set proper To and From headers for VAPI -->
      <action application="export" data="sip_from_uri=sip:919228030045@*************:5080"/>
      <action application="export" data="sip_to_uri=sip:919228030045@${vapi_domain}"/>
      
      <!-- Set contact header -->
      <action application="export" data="sip_contact_uri=sip:919228030045@*************:5080"/>
      
      <!-- Enable SIP tracing for this call -->
      <action application="set" data="sip_trace=yes"/>
      
      <!-- Log the headers we're sending -->
      <action application="log" data="INFO === VAPI HEADERS ==="/>
      <action application="log" data="INFO X-VAPI-Key: ${vapi_api_key}"/>
      <action application="log" data="INFO X-Version: ${vapi_version}"/>
      <action application="log" data="INFO From URI: sip:919228030045@*************:5080"/>
      <action application="log" data="INFO To URI: sip:919228030045@${vapi_domain}"/>
      
      <!-- Bridge to VAPI with proper formatting -->
      <action application="bridge" data="sofia/external/919228030045@${vapi_domain}"/>
      
      <!-- If bridge fails, log and hangup -->
      <action application="log" data="ERROR VAPI bridge failed: ${bridge_hangup_cause}"/>
      <action application="playback" data="tone_stream://%(500,0,480,620)"/>
      <action application="hangup" data="NORMAL_CLEARING"/>
    </condition>
  </extension>
  
  <!-- Fallback for any other numbers -->
  <extension name="catchall">
    <condition field="destination_number" expression="^(.*)$">
      <action application="log" data="INFO Unhandled number: ${destination_number}"/>
      <action application="answer"/>
      <action application="playback" data="tone_stream://%(500,0,480,620);loops=3"/>
      <action application="hangup" data="UNALLOCATED_NUMBER"/>
    </condition>
  </extension>
</include>
