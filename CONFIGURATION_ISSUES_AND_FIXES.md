# FreeSWITCH-VAPI Configuration Issues and Fixes

## Issues Found in Your Current Configuration

### 1. **CRITICAL: Wrong From Header**
**Your Current Code:**
```xml
<action application="set" data="sip_from_uri=sip:${caller_id_number}@*************:5080"/>
```

**Problem:** You're using `${caller_id_number}` (the original caller's number) in the From header. VAPI expects to see the registered number (919228030045) in the From header to identify the call.

**Fixed Code:**
```xml
<action application="export" data="nolocal:sip_from_uri=sip:919228030045@*************"/>
```

### 2. **CRITICAL: Using 'set' Instead of 'export' for SIP Headers**
**Your Current Code:**
```xml
<action application="set" data="sip_h_X-VAPI-Key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
<action application="set" data="sip_h_X-Version=2023-11-01"/>
```

**Problem:** Using `set` instead of `export` means these headers may not be passed to the outbound call to VAPI.

**Fixed Code:**
```xml
<action application="export" data="nolocal:sip_h_X-VAPI-Key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
<action application="export" data="nolocal:sip_h_X-Version=2023-11-01"/>
```

### 3. **CRITICAL: Missing 'nolocal:' Prefix**
**Your Current Code:**
```xml
<action application="set" data="sip_from_uri=..."/>
```

**Problem:** Without `nolocal:`, FreeSWITCH may not pass these headers to the bridged call.

**Fixed Code:**
```xml
<action application="export" data="nolocal:sip_from_uri=..."/>
```

### 4. **Wrong Bridge URI Format**
**Your Current Code:**
```xml
<action application="bridge" data="sofia/external/sip:<EMAIL>;transport=udp"/>
```

**Problem:** The `sip:` prefix in the bridge data is unnecessary and may cause issues.

**Fixed Code:**
```xml
<action application="bridge" data="sofia/external/<EMAIL>"/>
```

### 5. **Missing Answer Application**
**Your Current Code:** No `answer` application before bridge

**Problem:** Not answering the call before bridging can cause issues with some SIP services.

**Fixed Code:**
```xml
<action application="answer"/>
```

### 6. **Inconsistent Number Format**
**Your Current Code:** Uses `${caller_id_number}` in various places

**Problem:** VAPI needs to see the registered number (919228030045) consistently in all headers.

**Fixed Code:** Use `919228030045` consistently throughout.

## Key Fixes Applied

### 1. **Proper From Header**
```xml
<!-- OLD (WRONG) -->
<action application="set" data="sip_from_uri=sip:${caller_id_number}@*************:5080"/>

<!-- NEW (CORRECT) -->
<action application="export" data="nolocal:sip_from_uri=sip:919228030045@*************"/>
```

### 2. **Correct Header Export**
```xml
<!-- OLD (WRONG) -->
<action application="set" data="sip_h_X-VAPI-Key=..."/>

<!-- NEW (CORRECT) -->
<action application="export" data="nolocal:sip_h_X-VAPI-Key=..."/>
```

### 3. **Proper To Header**
```xml
<!-- NEW (ADDED) -->
<action application="export" data="nolocal:sip_to_uri=sip:<EMAIL>"/>
```

### 4. **Identity Headers**
```xml
<!-- NEW (ADDED) -->
<action application="export" data="nolocal:sip_h_P-Asserted-Identity=sip:919228030045@*************"/>
<action application="export" data="nolocal:sip_h_Remote-Party-ID=919228030045"/>
```

## Why These Fixes Work

1. **From Header with Registered Number**: VAPI looks at the From header to identify which registered number the call is for. Using the original caller's number confuses VAPI.

2. **Export vs Set**: `export` ensures headers are passed to the bridged call, while `set` only sets local variables.

3. **nolocal: Prefix**: Ensures headers are sent to the remote party (VAPI) and not just kept locally.

4. **Consistent Number Format**: Using 919228030045 everywhere ensures VAPI can match the call to the registered number.

## How to Apply the Fix

1. **Backup your current configuration:**
   ```bash
   cp /usr/local/freeswitch/conf/dialplan/public.xml /usr/local/freeswitch/conf/dialplan/public.xml.backup
   ```

2. **Replace your current VAPI extension with the fixed version:**
   - Remove the current `vapi_inbound` and `vapi_outbound` extensions
   - Add the `vapi_inbound_fixed` extension from `public_fixed.xml`

3. **Reload FreeSWITCH configuration:**
   ```bash
   fs_cli -x "reloadxml"
   ```

4. **Enable debugging:**
   ```bash
   fs_cli -x "sofia profile external siptrace on"
   fs_cli -x "console loglevel debug"
   ```

5. **Test the call and check logs**

## Expected Result

After applying these fixes, you should see:
- No more "phone number not found" errors
- VAPI responds with 200 OK
- Call connects to the AI assistant
- Proper audio flow in both directions

## Debugging Commands

If it still doesn't work, run these commands to debug:

```bash
# Check SIP profile status
fs_cli -x "sofia status profile external"

# Enable detailed SIP tracing
fs_cli -x "sofia global siptrace on"

# Check active calls
fs_cli -x "show calls"

# Test bridge manually
fs_cli -x "originate sofia/external/<EMAIL> &echo"
```

The most critical fix is ensuring the From header contains `sip:919228030045@*************` instead of the original caller's number.
