#!/bin/bash

# FreeSWITCH VAPI Fix Application Script
# This script applies the fix for the "phone number not found" error

set -e  # Exit on any error

echo "=== FreeSWITCH VAPI Fix Application ==="
echo "Date: $(date)"
echo ""

# Configuration
FREESWITCH_CONF_DIR="/usr/local/freeswitch/conf"
PUBLIC_XML_PATH="${FREESWITCH_CONF_DIR}/dialplan/public.xml"
BACKUP_PATH="${PUBLIC_XML_PATH}.backup.$(date +%Y%m%d_%H%M%S)"

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  This script needs to be run as root or with sudo"
    echo "   Please run: sudo $0"
    exit 1
fi

# Check if FreeSWITCH config directory exists
if [ ! -d "$FREESWITCH_CONF_DIR" ]; then
    echo "❌ FreeSWITCH configuration directory not found: $FREESWITCH_CONF_DIR"
    echo "   Please check your FreeSWITCH installation path"
    exit 1
fi

# Check if public.xml exists
if [ ! -f "$PUBLIC_XML_PATH" ]; then
    echo "❌ public.xml not found: $PUBLIC_XML_PATH"
    echo "   Please check your FreeSWITCH configuration"
    exit 1
fi

echo "✅ FreeSWITCH configuration found"
echo "   Config directory: $FREESWITCH_CONF_DIR"
echo "   Public XML: $PUBLIC_XML_PATH"
echo ""

# Create backup
echo "📁 Creating backup of current configuration..."
cp "$PUBLIC_XML_PATH" "$BACKUP_PATH"
echo "   Backup created: $BACKUP_PATH"
echo ""

# Check if the fixed configuration file exists
if [ ! -f "public_fixed.xml" ]; then
    echo "❌ Fixed configuration file 'public_fixed.xml' not found"
    echo "   Please ensure you have the public_fixed.xml file in the current directory"
    exit 1
fi

# Show current VAPI configuration
echo "🔍 Checking current VAPI configuration..."
if grep -q "vapi_inbound" "$PUBLIC_XML_PATH"; then
    echo "   ✅ Found existing VAPI configuration"
    
    # Check for the problematic patterns
    if grep -q "sip_from_uri=sip:\${caller_id_number}" "$PUBLIC_XML_PATH"; then
        echo "   ❌ Found problematic From header (uses caller_id_number)"
    fi
    
    if grep -q "application=\"set\".*sip_h_X-VAPI-Key" "$PUBLIC_XML_PATH"; then
        echo "   ❌ Found problematic header setting (uses 'set' instead of 'export')"
    fi
    
    if ! grep -q "nolocal:sip_h_X-VAPI-Key" "$PUBLIC_XML_PATH"; then
        echo "   ❌ Missing 'nolocal:' prefix for VAPI headers"
    fi
else
    echo "   ⚠️  No existing VAPI configuration found"
fi
echo ""

# Apply the fix
echo "🔧 Applying VAPI fix..."

# Remove old VAPI extensions and add the fixed one
python3 << 'EOF'
import re
import sys

# Read the current public.xml
with open('/usr/local/freeswitch/conf/dialplan/public.xml', 'r') as f:
    content = f.read()

# Read the fixed configuration
with open('public_fixed.xml', 'r') as f:
    fixed_content = f.read()

# Remove existing VAPI extensions
# Pattern to match VAPI extensions
vapi_pattern = r'<extension name="vapi_[^"]*">.*?</extension>'
content = re.sub(vapi_pattern, '', content, flags=re.DOTALL)

# Find the position to insert the new extension (after call_debug extension)
insert_pattern = r'(</extension>\s*\n\s*<!-- VAPI|</extension>\s*\n\s*<extension name="public_extensions")'
match = re.search(r'(<extension name="call_debug".*?</extension>)', content, re.DOTALL)

if match:
    # Extract the fixed VAPI extension from the fixed content
    vapi_match = re.search(r'(<extension name="vapi_inbound_fixed">.*?</extension>)', fixed_content, re.DOTALL)
    if vapi_match:
        vapi_extension = vapi_match.group(1)
        # Insert after call_debug extension
        insert_pos = match.end()
        new_content = content[:insert_pos] + '\n\n    ' + vapi_extension + '\n' + content[insert_pos:]
        
        # Write the updated content
        with open('/usr/local/freeswitch/conf/dialplan/public.xml', 'w') as f:
            f.write(new_content)
        
        print("✅ VAPI configuration updated successfully")
    else:
        print("❌ Could not extract VAPI extension from fixed configuration")
        sys.exit(1)
else:
    print("❌ Could not find insertion point in public.xml")
    sys.exit(1)
EOF

if [ $? -eq 0 ]; then
    echo "   ✅ Configuration file updated"
else
    echo "   ❌ Failed to update configuration"
    echo "   Restoring backup..."
    cp "$BACKUP_PATH" "$PUBLIC_XML_PATH"
    exit 1
fi

# Validate the XML
echo ""
echo "🔍 Validating XML syntax..."
if command -v xmllint >/dev/null 2>&1; then
    if xmllint --noout "$PUBLIC_XML_PATH" 2>/dev/null; then
        echo "   ✅ XML syntax is valid"
    else
        echo "   ❌ XML syntax error detected"
        echo "   Restoring backup..."
        cp "$BACKUP_PATH" "$PUBLIC_XML_PATH"
        exit 1
    fi
else
    echo "   ⚠️  xmllint not available, skipping XML validation"
fi

# Reload FreeSWITCH configuration
echo ""
echo "🔄 Reloading FreeSWITCH configuration..."
if command -v fs_cli >/dev/null 2>&1; then
    if fs_cli -x "reloadxml" >/dev/null 2>&1; then
        echo "   ✅ FreeSWITCH configuration reloaded"
    else
        echo "   ⚠️  Could not reload FreeSWITCH configuration"
        echo "   Please run manually: fs_cli -x 'reloadxml'"
    fi
else
    echo "   ⚠️  fs_cli not found, please reload manually"
    echo "   Run: fs_cli -x 'reloadxml'"
fi

# Enable debugging
echo ""
echo "🐛 Enabling SIP debugging..."
if command -v fs_cli >/dev/null 2>&1; then
    fs_cli -x "sofia profile external siptrace on" >/dev/null 2>&1
    fs_cli -x "console loglevel debug" >/dev/null 2>&1
    echo "   ✅ SIP tracing enabled"
else
    echo "   ⚠️  Please enable debugging manually:"
    echo "   fs_cli -x 'sofia profile external siptrace on'"
    echo "   fs_cli -x 'console loglevel debug'"
fi

echo ""
echo "🎉 VAPI fix applied successfully!"
echo ""
echo "📋 Summary of changes:"
echo "   ✅ Fixed From header to use VAPI registered number (919228030045)"
echo "   ✅ Changed 'set' to 'export' for SIP headers"
echo "   ✅ Added 'nolocal:' prefix to ensure headers reach VAPI"
echo "   ✅ Added proper To header with VAPI domain"
echo "   ✅ Added identity headers for better recognition"
echo "   ✅ Added answer application before bridge"
echo ""
echo "🧪 Next steps:"
echo "1. Make a test call to +919228030045"
echo "2. Check FreeSWITCH logs for VAPI call details"
echo "3. Verify that VAPI no longer returns 'phone number not found'"
echo ""
echo "📊 Monitor logs with:"
echo "   tail -f /usr/local/freeswitch/log/freeswitch.log | grep -i vapi"
echo ""
echo "🔙 If you need to rollback:"
echo "   cp $BACKUP_PATH $PUBLIC_XML_PATH"
echo "   fs_cli -x 'reloadxml'"
