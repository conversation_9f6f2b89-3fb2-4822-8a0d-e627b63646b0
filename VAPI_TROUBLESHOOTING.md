# FreeSWITCH-VAPI Integration Troubleshooting Guide

## Problem: "Phone Number Not Found" Error

This error typically occurs when VAPI receives the SIP call but cannot match it to a registered phone number. Here are the most common causes and solutions:

## Root Causes and Fixes

### 1. **Incorrect From Header Format**
**Problem**: The SIP From header doesn't contain the registered number
**Solution**: Ensure From header uses exact format: `sip:************@your-server-ip`

```xml
<action application="export" data="nolocal:sip_from_uri=sip:************@*************"/>
```

### 2. **Missing or Incorrect To Header**
**Problem**: The To header doesn't match VAPI's expected format
**Solution**: Set To header to use registered number at VAPI domain

```xml
<action application="export" data="nolocal:sip_to_uri=sip:<EMAIL>"/>
```

### 3. **Authentication Headers Missing**
**Problem**: VAPI authentication headers not present or incorrect
**Solution**: Add proper X-VAPI-Key and X-Version headers

```xml
<action application="export" data="nolocal:sip_h_X-VAPI-Key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
<action application="export" data="nolocal:sip_h_X-Version=2023-11-01"/>
```

### 4. **Number Format Mismatch**
**Problem**: Number format in SIP headers doesn't match VAPI registration
**Solution**: Use consistent format throughout: `************` (no + or country code variations)

### 5. **Request-URI Issues**
**Problem**: The SIP Request-URI doesn't contain the correct number
**Solution**: Ensure bridge command uses exact registered number

```xml
<action application="bridge" data="sofia/external/<EMAIL>"/>
```

## Debugging Steps

### Step 1: Enable SIP Tracing
```bash
fs_cli -x "sofia profile external siptrace on"
fs_cli -x "sofia global siptrace on"
fs_cli -x "console loglevel debug"
```

### Step 2: Check VAPI Registration
Verify in VAPI dashboard:
- Number is registered as: `************`
- API key is active: `ee2abd78-2c93-4fba-bb2e-1dba77944e40`
- SIP domain is correct: `a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai`

### Step 3: Capture SIP Messages
Make a test call and capture the exact SIP INVITE sent to VAPI:

```bash
# Run this on FreeSWITCH server
tcpdump -i any -s 0 -w vapi_sip.pcap host a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai
```

### Step 4: Verify Headers
Check that the SIP INVITE contains:
- `From: <sip:************@*************>`
- `To: <sip:<EMAIL>>`
- `X-VAPI-Key: ee2abd78-2c93-4fba-bb2e-1dba77944e40`
- `X-Version: 2023-11-01`

## Configuration Files

### Use the Fixed Dialplan
The `vapi_fixed.xml` dialplan includes all the necessary fixes:

1. Proper From/To header formatting
2. Correct authentication headers
3. Consistent number formatting
4. Additional identity headers

### Test Configuration
```bash
# Make the debug script executable
chmod +x debug_vapi.sh

# Run the debug script
./debug_vapi.sh
```

## Common Mistakes to Avoid

1. **Don't use +91 prefix** in SIP headers - use `************`
2. **Don't omit the nolocal:** prefix in export statements
3. **Don't use different number formats** in different headers
4. **Don't forget to answer the call** before bridging
5. **Don't use wrong VAPI domain** - must match exactly

## Testing the Fix

### Manual Test via FreeSWITCH CLI
```bash
fs_cli -x "originate sofia/external/<EMAIL> &echo"
```

### Expected Success Indicators
- No "phone number not found" error
- VAPI responds with 200 OK
- Call connects to AI assistant
- Audio flows both ways

## If Still Not Working

1. **Check VAPI logs** in their dashboard for exact error messages
2. **Verify network connectivity** to VAPI's SIP servers
3. **Test with different number formats** (try without country code)
4. **Contact VAPI support** with captured SIP traces
5. **Verify API key permissions** and account status

## Alternative Approaches

If the standard approach doesn't work, try:

1. **Use P-Asserted-Identity header** instead of From header
2. **Try different SIP profiles** (internal vs external)
3. **Use SIP registration** to VAPI instead of direct bridging
4. **Implement SIP B2BUA** for header manipulation

## Success Metrics

When working correctly, you should see:
- FreeSWITCH logs show successful bridge
- VAPI responds with 200 OK
- Call duration > 0 seconds
- AI assistant answers and responds
- No SIP error codes (4xx, 5xx)
