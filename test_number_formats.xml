    <!-- TEST: Try different number formats for VAPI -->
    <extension name="vapi_test_formats">
      <condition field="destination_number" expression="^(\+?919228030045|9228030045)$">
        
        <action application="answer"/>
        <action application="sleep" data="1000"/>
        
        <!-- Test 1: Try with +91 prefix -->
        <action application="set" data="test_number_1=+919228030045"/>
        <action application="set" data="test_number_2=919228030045"/>
        <action application="set" data="test_number_3=9228030045"/>
        
        <action application="set" data="vapi_domain=a2a78994-6f2d-4aae-a41e-a406472c8dc3.sip.vapi.ai"/>
        <action application="set" data="vapi_api_key=ee2abd78-2c93-4fba-bb2e-1dba77944e40"/>
        <action application="set" data="vapi_version=2023-11-01"/>
        <action application="set" data="vapi_credential_id=92fe1a64-b59f-4d60-b62d-6295d320a5bc"/>
        
        <!-- Try Format 1: +919228030045 -->
        <action application="log" data="INFO === TESTING FORMAT 1: +919228030045 ==="/>
        <action application="export" data="nolocal:sip_h_X-VAPI-Key=${vapi_api_key}"/>
        <action application="export" data="nolocal:sip_h_X-Version=${vapi_version}"/>
        <action application="export" data="nolocal:sip_h_X-Credential-ID=${vapi_credential_id}"/>
        <action application="export" data="nolocal:sip_h_X-Phone-Number=${test_number_1}"/>
        <action application="export" data="nolocal:sip_from_uri=sip:${test_number_1}@*************"/>
        <action application="export" data="nolocal:sip_to_uri=sip:${test_number_1}@${vapi_domain}"/>
        <action application="set" data="effective_caller_id_number=${test_number_1}"/>
        
        <action application="bridge" data="sofia/external/${test_number_1}@${vapi_domain}"/>
        
        <!-- If that fails, try Format 2: 919228030045 -->
        <action application="log" data="INFO === TESTING FORMAT 2: 919228030045 ==="/>
        <action application="export" data="nolocal:sip_h_X-Phone-Number=${test_number_2}"/>
        <action application="export" data="nolocal:sip_from_uri=sip:${test_number_2}@*************"/>
        <action application="export" data="nolocal:sip_to_uri=sip:${test_number_2}@${vapi_domain}"/>
        <action application="set" data="effective_caller_id_number=${test_number_2}"/>
        
        <action application="bridge" data="sofia/external/${test_number_2}@${vapi_domain}"/>
        
        <!-- If that fails, try Format 3: 9228030045 -->
        <action application="log" data="INFO === TESTING FORMAT 3: 9228030045 ==="/>
        <action application="export" data="nolocal:sip_h_X-Phone-Number=${test_number_3}"/>
        <action application="export" data="nolocal:sip_from_uri=sip:${test_number_3}@*************"/>
        <action application="export" data="nolocal:sip_to_uri=sip:${test_number_3}@${vapi_domain}"/>
        <action application="set" data="effective_caller_id_number=${test_number_3}"/>
        
        <action application="bridge" data="sofia/external/${test_number_3}@${vapi_domain}"/>
        
        <!-- All formats failed -->
        <action application="log" data="ERROR All number formats failed"/>
        <action application="playback" data="tone_stream://%(500,0,480,620);loops=3"/>
        <action application="hangup" data="CALL_REJECTED"/>
      </condition>
    </extension>
