# Fix VAPI Port Mismatch Issue

## Problem Identified
- **VAPI SIP Trunk**: Expects port 5060
- **FreeSWITCH**: Running on port 5080
- **Result**: VA<PERSON> can't properly route calls to FreeSWITCH

## Solution 1: Update VAPI SIP Trunk (RECOMMENDED)

### In VAPI Dashboard:
1. Go to your SIP Trunk settings
2. Change **Port** from `5060` to `5080`
3. Save the configuration

This matches your current FreeSWITCH setup.

## Solution 2: Update FreeSWITCH Port

### If you prefer to use standard SIP port 5060:

1. **Edit FreeSWITCH SIP profile:**
```bash
sudo nano /usr/local/freeswitch/conf/sip_profiles/external.xml
```

2. **Change the port:**
```xml
<!-- Change this line -->
<param name="sip-port" value="5080"/>
<!-- To this -->
<param name="sip-port" value="5060"/>
```

3. **Update your dialplan headers:**
```bash
sudo nano /usr/local/freeswitch/conf/dialplan/public.xml
```

4. **Change all references from 5080 to 5060:**
```xml
<!-- Change -->
<action application="export" data="nolocal:sip_from_uri=sip:919228030045@*************:5080"/>
<!-- To -->
<action application="export" data="nolocal:sip_from_uri=sip:919228030045@*************:5060"/>

<!-- And -->
<action application="export" data="nolocal:sip_contact_uri=sip:919228030045@*************:5080"/>
<!-- To -->
<action application="export" data="nolocal:sip_contact_uri=sip:919228030045@*************:5060"/>
```

5. **Restart FreeSWITCH:**
```bash
sudo systemctl restart freeswitch
```

6. **Update Cloud Connect configuration** to forward calls to port 5060 instead of 5080

## Why This Fixes the Issue

The "phone number not found" error occurs because:
1. VAPI receives the call correctly
2. VAPI tries to route it back to your FreeSWITCH
3. But VAPI is trying to connect to port 5060
4. Your FreeSWITCH is listening on port 5080
5. Connection fails, causing the error

## Recommended Approach

**Use Solution 1** (update VAPI to port 5080) because:
- ✅ No FreeSWITCH changes needed
- ✅ No Cloud Connect changes needed  
- ✅ Simpler and less disruptive
- ✅ Your current FreeSWITCH config is working

## After the Fix

Once you update the port in VAPI:
1. Test a call to +919228030045
2. You should hear the AI assistant instead of "phone number not found"
3. The beep should be replaced with actual conversation

## Verification Steps

After making the change:
1. Check VAPI dashboard shows the correct port (5080)
2. Make a test call
3. Monitor FreeSWITCH logs for successful connection
4. Verify AI assistant responds properly
